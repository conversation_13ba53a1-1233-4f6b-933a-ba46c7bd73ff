@page "/statistics-chart"
@using ApexCharts
@using System.Linq
@rendermode InteractiveServer
@inject IJSRuntime JS

<PageTitle>統計圖表頁面</PageTitle>
<MyPageTitle Title="統計圖表展示"></MyPageTitle>

<div class="container-fluid">
    <div class="row">
        <!-- 主要統計圖表 -->
        <div class="col-12 col-lg-8 mb-4">
            <MyCard_white Title="銷售數據統計" Css="h-100">
                <div style="height: 400px;">
                    <ApexChart TItem="SalesData"
                               Title="月度銷售統計"
                               Options="chartOptions">
                        <ApexPointSeries TItem="SalesData"
                                         Items="salesData"
                                         SeriesType="currentChartType"
                                         Name="銷售額"
                                         XValue="@(e => e.Month)"
                                         YValue="@(e => e.Amount)"
                                         OrderBy="e => e.Month" />
                    </ApexChart>
                </div>
            </MyCard_white>
        </div>

        <!-- 控制面板 -->
        <div class="col-12 col-lg-4 mb-4">
            <MyCard_white Title="控制面板" Css="h-100">
                <div class="d-grid gap-3">
                    <div>
                        <h6 class="fw-bold">圖表類型</h6>
                        <div class="btn-group w-100" role="group">
                            <button type="button" 
                                    class="btn @(currentChartType == SeriesType.Column ? "btn-primary" : "btn-outline-primary")"
                                    @onclick="() => ChangeChartType(SeriesType.Column)">
                                柱狀圖
                            </button>
                            <button type="button" 
                                    class="btn @(currentChartType == SeriesType.Line ? "btn-primary" : "btn-outline-primary")"
                                    @onclick="() => ChangeChartType(SeriesType.Line)">
                                線圖
                            </button>
                            <button type="button" 
                                    class="btn @(currentChartType == SeriesType.Area ? "btn-primary" : "btn-outline-primary")"
                                    @onclick="() => ChangeChartType(SeriesType.Area)">
                                面積圖
                            </button>
                        </div>
                    </div>

                    <div>
                        <h6 class="fw-bold">數據操作</h6>
                        <button class="btn btn-success w-100 mb-2" @onclick="GenerateRandomData">
                            <i class="fa-solid fa-refresh"></i> 重新生成數據
                        </button>
                        <button class="btn btn-info w-100" 
                                data-bs-toggle="modal" 
                                data-bs-target="#chartModal">
                            <i class="fa-solid fa-chart-line"></i> 詳細統計圖表
                        </button>
                    </div>

                    <div>
                        <h6 class="fw-bold">統計摘要</h6>
                        <div class="row g-2">
                            <div class="col-6">
                                <div class="card bg-light">
                                    <div class="card-body p-2 text-center">
                                        <small class="text-muted">總銷售額</small>
                                        <div class="fw-bold">@totalSales.ToString("C0")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="card bg-light">
                                    <div class="card-body p-2 text-center">
                                        <small class="text-muted">平均值</small>
                                        <div class="fw-bold">@averageSales.ToString("C0")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="card bg-light">
                                    <div class="card-body p-2 text-center">
                                        <small class="text-muted">最高值</small>
                                        <div class="fw-bold">@maxSales.ToString("C0")</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="card bg-light">
                                    <div class="card-body p-2 text-center">
                                        <small class="text-muted">最低值</small>
                                        <div class="fw-bold">@minSales.ToString("C0")</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </MyCard_white>
        </div>
    </div>
</div>

<!-- Bootstrap 5 互動視窗 -->
<div class="modal fade" id="chartModal" tabindex="-1" aria-labelledby="chartModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chartModalLabel">
                    <i class="fa-solid fa-chart-line"></i> 詳細統計圖表
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- 圓餅圖 -->
                    <div class="col-12 col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">銷售分布 - 圓餅圖</h6>
                            </div>
                            <div class="card-body">
                                <div style="height: 300px;">
                                    <ApexChart TItem="SalesData"
                                               Options="pieChartOptions">
                                        <ApexPointSeries TItem="SalesData"
                                                         Items="salesData"
                                                         SeriesType="SeriesType.Pie"
                                                         Name="銷售分布"
                                                         XValue="@(e => e.Month)"
                                                         YValue="@(e => e.Amount)" />
                                    </ApexChart>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 雷達圖 -->
                    <div class="col-12 col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">銷售表現 - 雷達圖</h6>
                            </div>
                            <div class="card-body">
                                <div style="height: 300px;">
                                    <ApexChart TItem="SalesData"
                                               Options="radarChartOptions">
                                        <ApexPointSeries TItem="SalesData"
                                                         Items="salesData"
                                                         SeriesType="SeriesType.Radar"
                                                         Name="銷售表現"
                                                         XValue="@(e => e.Month)"
                                                         YValue="@(e => e.Amount)" />
                                    </ApexChart>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 數據表格 -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">詳細數據表格</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>月份</th>
                                                <th>銷售額</th>
                                                <th>成長率</th>
                                                <th>狀態</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var item in salesData.OrderBy(x => x.Month))
                                            {
                                                <tr>
                                                    <td>@item.Month</td>
                                                    <td>@item.Amount.ToString("C0")</td>
                                                    <td>
                                                        @{
                                                            var growthRate = CalculateGrowthRate(item);
                                                        }
                                                        <span class="badge @(growthRate >= 0 ? "bg-success" : "bg-danger")">
                                                            @growthRate.ToString("F1")%
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @if (item.Amount >= averageSales)
                                                        {
                                                            <span class="badge bg-success">優秀</span>
                                                        }
                                                        else if (item.Amount >= averageSales * 0.8)
                                                        {
                                                            <span class="badge bg-warning">良好</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-danger">需改善</span>
                                                        }
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                <button type="button" class="btn btn-primary" @onclick="ExportData">
                    <i class="fa-solid fa-download"></i> 匯出數據
                </button>
            </div>
        </div>
    </div>
</div>

@code {
    // 數據模型
    public class SalesData
    {
        public string Month { get; set; } = "";
        public decimal Amount { get; set; }
    }

    // 數據和變數
    private List<SalesData> salesData = new();
    private SeriesType currentChartType = SeriesType.Column;
    private decimal totalSales = 0;
    private decimal averageSales = 0;
    private decimal maxSales = 0;
    private decimal minSales = 0;

    // 圖表選項
    private ApexChartOptions<SalesData> chartOptions = new();
    private ApexChartOptions<SalesData> pieChartOptions = new();
    private ApexChartOptions<SalesData> radarChartOptions = new();

    protected override void OnInitialized()
    {
        GenerateInitialData();
        SetupChartOptions();
        CalculateStatistics();
    }

    private void GenerateInitialData()
    {
        var random = new Random();
        var months = new[] { "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" };
        
        salesData = months.Select(month => new SalesData
        {
            Month = month,
            Amount = random.Next(50000, 200000)
        }).ToList();
    }

    private void GenerateRandomData()
    {
        var random = new Random();
        foreach (var item in salesData)
        {
            item.Amount = random.Next(50000, 200000);
        }
        CalculateStatistics();
        StateHasChanged();
    }

    private void CalculateStatistics()
    {
        if (salesData.Any())
        {
            totalSales = salesData.Sum(x => x.Amount);
            averageSales = salesData.Average(x => x.Amount);
            maxSales = salesData.Max(x => x.Amount);
            minSales = salesData.Min(x => x.Amount);
        }
    }

    private void ChangeChartType(SeriesType newType)
    {
        currentChartType = newType;
        StateHasChanged();
    }

    private double CalculateGrowthRate(SalesData current)
    {
        var currentIndex = salesData.IndexOf(current);
        if (currentIndex == 0) return 0;
        
        var previous = salesData[currentIndex - 1];
        return (double)((current.Amount - previous.Amount) / previous.Amount * 100);
    }

    private void SetupChartOptions()
    {
        // 主圖表選項
        chartOptions = new ApexChartOptions<SalesData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Chart = new Chart
            {
                Toolbar = new Toolbar { Show = true },
                Background = "transparent"
            },
            Colors = new List<string> { "#3498db", "#e74c3c", "#2ecc71", "#f39c12" },
            DataLabels = new DataLabels { Enabled = true },
            Xaxis = new XAxis
            {
                Title = new AxisTitle { Text = "月份" }
            },
            Yaxis = new List<YAxis>
            {
                new YAxis
                {
                    Title = new AxisTitle { Text = "銷售額 (NT$)" },
                    Labels = new YAxisLabels
                    {
                        Formatter = "function(value) { return 'NT$' + value.toLocaleString(); }"
                    }
                }
            }
        };

        // 圓餅圖選項
        pieChartOptions = new ApexChartOptions<SalesData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Chart = new Chart { Background = "transparent" },
            Colors = new List<string> { "#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6", "#1abc9c", "#34495e", "#f1c40f", "#e67e22", "#95a5a6", "#16a085", "#27ae60" },
            Legend = new Legend { Position = LegendPosition.Bottom }
        };

        // 雷達圖選項
        radarChartOptions = new ApexChartOptions<SalesData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Chart = new Chart { Background = "transparent" },
            Colors = new List<string> { "#3498db" },
            Xaxis = new XAxis
            {
                Categories = salesData.Select(x => x.Month).ToList()
            }
        };
    }

    private async Task ExportData()
    {
        // 這裡可以實現數據匯出功能
        await JS.InvokeVoidAsync("showColoredToast", "success", "數據匯出功能開發中...");
    }
}
