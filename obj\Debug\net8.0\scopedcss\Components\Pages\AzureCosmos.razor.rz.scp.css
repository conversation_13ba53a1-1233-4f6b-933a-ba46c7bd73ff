/* 風格2：優雅極簡風格 */
.elegant-tabs[b-tmwuqn7iug] {
    background: transparent;
    border: none;
    border-bottom: 1px solid #444;
    margin-bottom: 0;
}

.elegant-tab[b-tmwuqn7iug] {
    background: transparent;
    border: none;
    color: #C4E1E1;
    font-weight: 500;
    padding: 16px 32px;
    position: relative;
    transition: all 0.3s ease;
    text-decoration: none;
    font-weight: bold;
}

    .elegant-tab[b-tmwuqn7iug]::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, #3585D4, #66B3FF);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    .elegant-tab:hover[b-tmwuqn7iug] {
        color: #3585D4;
        background: rgba(53, 133, 212, 0.05);
    }

        .elegant-tab:hover[b-tmwuqn7iug]::after {
            width: 80%;
        }

    .elegant-tab.active[b-tmwuqn7iug] {
        color: #85C2FF;
        background: rgba(53, 133, 212, 0.1);
    }

        .elegant-tab.active[b-tmwuqn7iug]::after {
            width: 100%;
            background: linear-gradient(90deg, #3585D4, #66B3FF, #3585D4);
        }