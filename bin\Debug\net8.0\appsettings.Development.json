{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    //測試
    "Oracle": "User ID=*********;Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=***************)(PORT=1521)))(CONNECT_DATA=(SID=tcxweb)(SERVER=DEDICATED)));Password=*********.TEST;Pooling=true",
    "dbASH": "Server=**************;Database=ASH_DengueFever;User ID=*********;Password=*********;TrustServerCertificate=true;" // 測試
    //地端正式
    //"Oracle": "User ID=asx_web;Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=oracle-tcxweb.taiwannorth.cloudapp.azure.com)(PORT=1521)))(CONNECT_DATA=(SID=tcxweb)(SERVER=DEDICATED)));Password=************;Pooling=true"
  },
  "SysSetting": {
    "SysUrl": "/",
    "DownLoadUrl": "api"
  },
  "CosmosDb": { // Azure Cosmos DB 設定
    "EndpointUri": "https://yuntech-japan-nosql.documents.azure.com:443/", // ★ 端點
    "PrimaryKey": "****************************************************************************************", // ★ 主要金鑰
    "DatabaseId": "Dashboard" // 資料庫名稱
  }
}
