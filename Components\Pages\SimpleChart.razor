@page "/simple-chart"
@using ApexCharts
@using System.Linq
@rendermode InteractiveServer

<PageTitle>簡單統計圖表</PageTitle>
<MyPageTitle Title="簡單統計圖表"></MyPageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">銷售數據統計</h5>
                </div>
                <div class="card-body">
                    <!-- 圖表類型切換按鈕 -->
                    <div class="text-center mt-3 mb-3">
                        <div class="btn-group" role="group">
                            <button type="button"
                                    class="btn @(currentChartType == SeriesType.Bar ? "btn-primary" : "btn-outline-primary")"
                                    @onclick="() => ChangeChartType(SeriesType.Bar)">
                                <i class="fa-solid fa-chart-column"></i> 長條圖
                            </button>
                            <button type="button"
                                    class="btn @(currentChartType == SeriesType.Line ? "btn-primary" : "btn-outline-primary")"
                                    @onclick="() => ChangeChartType(SeriesType.Line)">
                                <i class="fa-solid fa-chart-line"></i> 折線圖
                            </button>
                        </div>
                    </div>

                    <!-- 互動視窗按鈕 -->
                    <div class="text-center">
                        <button class="btn btn-success"
                                data-bs-toggle="modal"
                                data-bs-target="#chartModal">
                            <i class="fa-solid fa-magnifying-glass-plus"></i> 查看詳細圖表
                        </button>
                    </div>
                    <!-- 統計圖表 -->
                    <div>
                        <ApexChart TItem="SalesData"
                                   Title="月度銷售統計"
                                   Options="chartOptions">
                            <ApexPointSeries TItem="SalesData"
                                             Items="salesData"
                                             SeriesType="currentChartType"
                                             Name="銷售額"
                                             XValue="@(e => e.Month)"
                                             YValue="@(e => e.Amount)" />
                        </ApexChart>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 5 互動視窗 -->
<div class="modal fade" id="chartModal" tabindex="-1" aria-labelledby="chartModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="chartModalLabel">
                    <i class="fa-solid fa-chart-line"></i> 詳細統計圖表
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 互動視窗內的相同圖表 -->
                <div class="col-6">
                    <ApexChart TItem="SalesData"
                               Title="月度銷售統計 - 詳細檢視"
                               Options="modalChartOptions">
                        <ApexPointSeries TItem="SalesData"
                                         Items="salesData"
                                         SeriesType="currentChartType"
                                         Name="銷售額"
                                         XValue="@(e => e.Month)"
                                         YValue="@(e => e.Amount)" />
                    </ApexChart>
                </div>
                
                <!-- 數據表格 -->
                <div class="mt-4">
                    <h6>數據詳情</h6>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>月份</th>
                                    <th>銷售額</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in salesData.OrderBy(x => x.Month))
                                {
                                    <tr>
                                        <td>@item.Month</td>
                                        <td>@item.Amount.ToString("C0")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
            </div>
        </div>
    </div>
</div>

@code {
    // 數據模型
    public class SalesData
    {
        public string Month { get; set; } = "";
        public decimal Amount { get; set; }
    }

    // 數據和變數
    private List<SalesData> salesData = new();
    private SeriesType currentChartType = SeriesType.Bar;

    // 圖表選項
    private ApexChartOptions<SalesData> chartOptions = new();
    private ApexChartOptions<SalesData> modalChartOptions = new();

    protected override void OnInitialized()
    {
        GenerateData();
        SetupChartOptions();
    }

    private void GenerateData()
    {
        var random = new Random();
        var months = new[] { "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" };
        
        salesData = months.Select(month => new SalesData
        {
            Month = month,
            Amount = random.Next(50000, 200000)
        }).ToList();
    }

    private void ChangeChartType(SeriesType newType)
    {
        currentChartType = newType;
        StateHasChanged();
    }

    private void SetupChartOptions()
    {
        // 主頁面圖表選項
        chartOptions = new ApexChartOptions<SalesData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Chart = new Chart
            {
                Toolbar = new Toolbar { Show = true },
                Background = "transparent"
            },
            Colors = new List<string> { "#3498db" },
            DataLabels = new DataLabels { Enabled = true },
            Xaxis = new XAxis
            {
                Title = new AxisTitle { Text = "月份" }
            },
            Yaxis = new List<YAxis>
            {
                new YAxis
                {
                    Title = new AxisTitle { Text = "銷售額 (NT$)" },
                    Labels = new YAxisLabels
                    {
                        Formatter = "function(value) { return 'NT$' + value.toLocaleString(); }"
                    }
                }
            }
        };

        // 互動視窗圖表選項
        modalChartOptions = new ApexChartOptions<SalesData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Chart = new Chart
            {
                Toolbar = new Toolbar { Show = true },
                Background = "transparent"
            },
            Colors = new List<string> { "#e74c3c" },
            DataLabels = new DataLabels { Enabled = true },
            Xaxis = new XAxis
            {
                Title = new AxisTitle { Text = "月份" }
            },
            Yaxis = new List<YAxis>
            {
                new YAxis
                {
                    Title = new AxisTitle { Text = "銷售額 (NT$)" },
                    Labels = new YAxisLabels
                    {
                        Formatter = "function(value) { return 'NT$' + value.toLocaleString(); }"
                    }
                }
            }
        };
    }
}
