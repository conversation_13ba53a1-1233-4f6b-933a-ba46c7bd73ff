@using ApexCharts
@using System.Linq

<!-- Bootstrap 5 互動視窗 -->
<div class="modal fade" id="@ModalId" tabindex="-1" aria-labelledby="@($"{ModalId}Label")" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="@($"{ModalId}Label")">
                    <i class="fa-solid fa-chart-line"></i> @Title
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 互動視窗內的圖表 -->
                <div style="height: 400px;">
                    @if (shouldRenderModalChart && ChartData != null && ChartData.Any())
                    {
                        <ApexChart TItem="TItem"
                                   Title="@ChartTitle"
                                   Options="ChartOptions">
                            <ApexPointSeries TItem="TItem"
                                             Items="ChartData"
                                             SeriesType="CurrentChartType"
                                             Name="@SeriesName"
                                             XValue="XValueSelector"
                                             YValue="YValueSelector" />
                        </ApexChart>
                    }
                </div>
                
                <!-- 額外內容 -->
                @if (AdditionalContent != null)
                {
                    <div class="mt-4">
                        @AdditionalContent
                    </div>
                }
                
                <!-- 數據表格 -->
                @if (ShowDataTable && TableData != null && TableData.Any())
                {
                    <div class="mt-4">
                        <h6>數據詳情</h6>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        @foreach (var column in TableColumns ?? new List<string>())
                                        {
                                            <th>@column</th>
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var row in TableData)
                                    {
                                        <tr>
                                            @foreach (var column in TableColumns ?? new List<string>())
                                            {
                                                <td>
                                                    @if (row.ContainsKey(column))
                                                    {
                                                        @FormatCellValue(row[column])
                                                    }
                                                </td>
                                            }
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }
            </div>
            <div class="modal-footer">
                @if (FooterContent != null)
                {
                    @FooterContent
                }
                else
                {
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                }
            </div>
        </div>
    </div>
</div>

@code {
    @typeparam TItem where TItem : class

    [Parameter] public string ModalId { get; set; } = "chartModal";
    [Parameter] public string Title { get; set; } = "詳細圖表";
    [Parameter] public string ChartTitle { get; set; } = "圖表詳細檢視";
    [Parameter] public string SeriesName { get; set; } = "數據";
    
    // 圖表相關參數
    [Parameter] public List<TItem>? ChartData { get; set; }
    [Parameter] public ApexChartOptions<TItem>? ChartOptions { get; set; }
    [Parameter] public SeriesType CurrentChartType { get; set; } = SeriesType.Bar;
    [Parameter] public Func<TItem, object>? XValueSelector { get; set; }
    [Parameter] public Func<TItem, decimal?>? YValueSelector { get; set; }
    
    // 表格相關參數
    [Parameter] public bool ShowDataTable { get; set; } = true;
    [Parameter] public List<Dictionary<string, object>>? TableData { get; set; }
    [Parameter] public List<string>? TableColumns { get; set; }
    
    // 額外內容
    [Parameter] public RenderFragment? AdditionalContent { get; set; }
    [Parameter] public RenderFragment? FooterContent { get; set; }
    
    // 控制渲染
    private bool shouldRenderModalChart = true;

    protected override void OnParametersSet()
    {
        // 當參數變更時重新渲染圖表
        shouldRenderModalChart = false;
        StateHasChanged();
        
        Task.Delay(50).ContinueWith(_ =>
        {
            InvokeAsync(() =>
            {
                shouldRenderModalChart = true;
                StateHasChanged();
            });
        });
    }

    private string FormatCellValue(object value)
    {
        if (value == null) return "";
        
        if (value is decimal decimalValue)
        {
            return decimalValue.ToString("C0");
        }
        
        return value.ToString() ?? "";
    }
}
