@inject NavigationManager NavManager
@inject IJSRuntime JS
@inject IConfiguration Configuration

@* 手機選單 *@
@if (IsMobile)
{
    <div class="side-bar-menu text-white">
        <a class="sidebar-item-collapse mt-2 ps-2" data-bs-toggle="collapse" href="#collapseItem-sm" id="sidebarCollapseToggle-sm">
            <span class="collapse-long-text ms-2">本校各處指標</span>
            <div class="d-flex align-items-center">
                <span class="collapse-short-text">本校</span>
                <span class="text-sm"><i class="fa-solid fa-angle-down"></i></span>
            </div>
        </a>

        <div class="collapse mt-2" id="collapseItem-sm">
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-graduation-cap"></i></span>
                <span class="menu-item-text">教務指標</span>
                <span class="menu-item-short-text">教務</span>
            </div>
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-user-graduate"></i></span>
                <span class="menu-item-text">學務指標</span>
                <span class="menu-item-short-text">學務</span>
            </div>
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-building-columns"></i></span>
                <span class="menu-item-text">總務指標</span>
                <span class="menu-item-short-text">總務</span>
            </div>
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-flask"></i></span>
                <span class="menu-item-text">研發指標</span>
                <span class="menu-item-short-text">研發</span>
            </div>
            <NavLink class="side-bar-menu-item" href="res-iaplanmoney" @onclick="PreserveMenuState">
                <span class="menu-item-icon"><i class="fa-solid fa-database"></i></span>
                <span class="menu-item-text">研究經費計畫資料</span>
                <span class="menu-item-short-text">研究經費</span>
            </NavLink>
            <NavLink class="side-bar-menu-item" href="res-combine-money-chart" @onclick="PreserveMenuState">
                <span class="menu-item-icon"><i class="fa-solid fa-chart-bar"></i></span>
                <span class="menu-item-text">產學合作計畫經費</span>
                <span class="menu-item-short-text">產學經費</span>
            </NavLink>
            <NavLink class="side-bar-menu-item" href="library-chart" @onclick="PreserveMenuState">
                <span class="menu-item-icon"><i class="fa-solid fa-book"></i></span>
                <span class="menu-item-text">圖書館統計</span>
                <span class="menu-item-short-text">圖書館</span>
            </NavLink>
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-globe"></i></span>
                <span class="menu-item-text">國際處指標</span>
                <span class="menu-item-short-text">國際處</span>
            </div>
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-handshake"></i></span>
                <span class="menu-item-text">產學指標</span>
                <span class="menu-item-short-text">產學</span>
            </div>
        </div>

        <a class="sidebar-item-collapse border mt-2 ps-2" data-bs-toggle="collapse" href="#collapseItem2-sm" id="sidebarCollapseToggle2-sm">
            <span class="collapse-long-text ms-2">四科大比較</span>
            <div class="d-flex align-items-center">
                <span class="collapse-short-text">四所</span>
                <span class="text-sm"><i class="fa-solid fa-angle-down"></i></span>
            </div>
        </a>

        <div class="collapse mt-2" id="collapseItem2-sm">
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-user-graduate"></i></span>
                <span class="menu-item-text">學生類</span>
                <span class="menu-item-short-text">學生</span>
            </div>
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-graduation-cap"></i></span>
                <span class="menu-item-text">教務類</span>
                <span class="menu-item-short-text">教務</span>
            </div>
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-flask-vial"></i></span>
                <span class="menu-item-text">研究類</span>
                <span class="menu-item-short-text">研究</span>
            </div>
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-school"></i></span>
                <span class="menu-item-text">校務類</span>
                <span class="menu-item-short-text">校務</span>
            </div>
            <div class="side-bar-menu-item">
                <span class="menu-item-icon"><i class="fa-solid fa-money-bill-trend-up"></i></span>
                <span class="menu-item-text">財務類</span>
                <span class="menu-item-short-text">財務</span>
            </div>
        </div>
    </div>
}
else
{
    <div class="side-bar-menu">
        @* <NavLink class="side-bar-menu-item" href="excel-visualization" @onclick="PreserveMenuState">
            <span class="menu-item-icon"><i class="fa-solid fa-house"></i></span>
            <span class="menu-item-text">首頁複製</span>
            <span class="menu-item-short-text">首頁複製</span>
        </NavLink> *@

        @*
        <NavLink class="side-bar-menu-item" href="test-page" @onclick="PreserveMenuState">
            <span class="menu-item-icon"><i class="fa-solid fa-chart-column"></i></span>
            <span class="menu-item-text">測試頁</span>
            <span class="menu-item-short-text">測試頁</span>
        </NavLink>

        <NavLink class="side-bar-menu-item" href="radialbar-example" @onclick="PreserveMenuState">
            <span class="menu-item-icon"><i class="fa-solid fa-chart-column"></i></span>
            <span class="menu-item-text">radialbar</span>
            <span class="menu-item-short-text">radialbar</span>
        </NavLink> *@

         @* <NavLink class="side-bar-menu-item" href="excelviewer" @onclick="PreserveMenuState">
             <span class="menu-item-icon"><i class="fa-solid fa-chart-column"></i></span>
             <span class="menu-item-text">excel</span>
             <span class="menu-item-short-text">excel</span>
         </NavLink>
         <NavLink class="side-bar-menu-item" href="deadlineSet" @onclick="PreserveMenuState">
             <span class="menu-item-icon"><i class="fa-solid fa-bicycle"></i></span>
             <span class="menu-item-text">系務助理登錄期限設定</span>
             <span class="menu-item-short-text">期限</span>
         </NavLink>
         <NavLink class="side-bar-menu-item" href="cadreSarch" @onclick="PreserveMenuState">
             <span class="menu-item-icon"><i class="fa-solid fa-bicycle"></i></span>
             <span class="menu-item-text">班級幹部查詢</span>
             <span class="menu-item-short-text">幹部</span>
         </NavLink>  *@

        <!--折疊1-->
        <div class="sidebar-item-collapse-lg d-flex align-content-center mb-3 mt-2">
            <span class="border rounded border-4 borderTitle me-2 align-text-bottom"></span>
            <span class="collapse-long-text ms-2">本校各處指標</span>
            <span class="collapse-short-text">本校</span>
        </div>

        <!--首頁-->
        <NavLink class="side-bar-menu-item" href="@Configuration["SysSetting:SysUrl"]" @onclick="PreserveMenuState" Match="NavLinkMatch.All">
            <span class="menu-item-icon"><i class="fa-solid fa-server"></i></span>
            <span class="menu-item-text">圖資處</span>
            <span class="menu-item-short-text">圖資處</span>
        </NavLink>

        <NavLink class="side-bar-menu-item" href="azureCosmos" @onclick="PreserveMenuState" Match="NavLinkMatch.All">
            <span class="menu-item-icon"><i class="fa-solid fa-database"></i></span>
            <span class="menu-item-text">圖資處2</span>
            <span class="menu-item-short-text">圖資處</span>
        </NavLink>

        <NavLink class="side-bar-menu-item" href="Internet" @onclick="PreserveMenuState">
            <span class="menu-item-icon"><i class="fa-solid fa-database"></i></span>
            <span class="menu-item-text">網路流量</span>
            <span class="menu-item-short-text">Azure Cosmos</span>
        </NavLink>

        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-graduation-cap"></i></span>
            <span class="menu-item-text">教務指標</span>
            <span class="menu-item-short-text">教務</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-user-graduate"></i></span>
            <span class="menu-item-text">學務指標</span>
            <span class="menu-item-short-text">學務</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-building-columns"></i></span>
            <span class="menu-item-text">總務指標</span>
            <span class="menu-item-short-text">總務</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-flask"></i></span>
            <span class="menu-item-text">研發指標</span>
            <span class="menu-item-short-text">研發</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-globe"></i></span>
            <span class="menu-item-text">國際處指標</span>
            <span class="menu-item-short-text">國際處</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-handshake"></i></span>
            <span class="menu-item-text">產學指標</span>
            <span class="menu-item-short-text">產學</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-book-open"></i></span>
            <span class="menu-item-text">高教深耕</span>
            <span class="menu-item-short-text">高教</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-building-columns"></i></span>
            <span class="menu-item-text">總務指標</span>
            <span class="menu-item-short-text">總務</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-coins"></i></span>
            <span class="menu-item-text">財務指標</span>
            <span class="menu-item-short-text">財務</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-users"></i></span>
            <span class="menu-item-text">人事指標</span>
            <span class="menu-item-short-text">人事</span>
        </div>

        <div class="sidebar-item-collapse-lg d-flex align-content-center mb-3">
            <span class="border rounded border-4 borderTitle me-2 align-text-bottom"></span>
            <span class="collapse-long-text ms-2">四科大比較</span>
            <span class="collapse-short-text">四所</span>
        </div>

        <!--首頁-->
        <NavLink class="side-bar-menu-item" href="tcs" @onclick="PreserveMenuState">
            <span class="menu-item-icon"><i class="fa-solid fa-house"></i></span>
            <span class="menu-item-text">四科大比較</span>
            <span class="menu-item-short-text">四科大</span>
        </NavLink>

        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-user-graduate"></i></span>
            <span class="menu-item-text">學生類</span>
            <span class="menu-item-short-text">學生</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-graduation-cap"></i></span>
            <span class="menu-item-text">教務類</span>
            <span class="menu-item-short-text">教務</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-flask-vial"></i></span>
            <span class="menu-item-text">研究類</span>
            <span class="menu-item-short-text">研究</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-school"></i></span>
            <span class="menu-item-text">校務類</span>
            <span class="menu-item-short-text">校務</span>
        </div>
        <div class="side-bar-menu-item">
            <span class="menu-item-icon"><i class="fa-solid fa-money-bill-trend-up"></i></span>
            <span class="menu-item-text">財務類</span>
            <span class="menu-item-short-text">財務</span>
        </div>

        <NavLink class="side-bar-menu-item" href="cadreSarch" @onclick="PreserveMenuState">
            <span class="menu-item-icon"><i class="fa-solid fa-server"></i></span>
            <span class="menu-item-text">導覽列</span>
            <span class="menu-item-short-text">導覽列</span>
        </NavLink>

        <NavLink class="side-bar-menu-item" href="simple-chart" @onclick="PreserveMenuState">
            <span class="menu-item-icon"><i class="fa-solid fa-chart-line"></i></span>
            <span class="menu-item-text">統計圖表</span>
            <span class="menu-item-short-text">統計圖</span>
        </NavLink>

        @* <NavLink class="side-bar-menu-item" href="tcs-new" @onclick="PreserveMenuState">
             <span class="menu-item-icon"><i class="fa-solid fa-server"></i></span>
             <span class="menu-item-text">圖資處 (舊版)</span>
             <span class="menu-item-short-text">圖資處舊</span>
         </NavLink> *@
    </div>
}

@code {
    [Parameter]
    public bool IsMobile { get; set; } = false;

    // 導航前保存側邊欄狀態的方法
    private async Task PreserveMenuState()
    {
        // 導航後調用JS函數重新應用側邊欄狀態
        await Task.Delay(10); // 簡短延遲以確保導航開始
        await JS.InvokeVoidAsync("applySidebarState");
    }
}
