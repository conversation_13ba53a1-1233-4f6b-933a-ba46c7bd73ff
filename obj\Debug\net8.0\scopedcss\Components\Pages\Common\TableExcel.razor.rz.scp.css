.excel-table-wrapper[b-nlr84jmxy5] {
    width: 100%;
    overflow-x: auto;
    /* 這會使此元素忽略父元素中定義的滾動條樣式 */
    scrollbar-width: auto;
}

/* 為Excel表格添加獨立的滾動條樣式，這將覆蓋全域樣式 */
.excel-table-wrapper[b-nlr84jmxy5]::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
}

.excel-table-wrapper[b-nlr84jmxy5]::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 4px !important;
}

.excel-table-wrapper[b-nlr84jmxy5]::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 4px !important;
}

.excel-table-wrapper[b-nlr84jmxy5]::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

.excel-table[b-nlr84jmxy5] {
    min-width: 1000px; /* 設定最小寬度，確保表格不會太擠 */
    width: 100%;
}

    .excel-table th[b-nlr84jmxy5] {
        padding: 10px 15px !important;
        white-space: nowrap; /* 標題不換行 */
        min-width: 100px; /* 標題最小寬度 */
        background-color: #f5f5f5 !important;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .excel-table td[b-nlr84jmxy5] {
        padding: 8px 15px !important;
    }
