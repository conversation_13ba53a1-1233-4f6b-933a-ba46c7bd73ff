
[b-01ifdea9l5] tr {
    height: 2.5em; /* 設定每列的高度，讓表格更整齊 */
}


    /* 隱藏空白的資料列 */
    [b-01ifdea9l5] tr:has(> td:not(:empty)) > td {
        display: table-cell;
    }

[b-01ifdea9l5] td:empty {
    display: none;
}


/* 使用 ::deep 來確保 CSS 能夠影響 Blazor QuickGrid 內的 Paginator */
/*::deep .paginator button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;*/ /* 設定固定寬度，避免變形 */
    /*height: 35px;
    border-radius: 5px;
    background-color: #f8f9fa;
    border: 1px solid #ccc;
    transition: background-color 0.2s;
}

    ::deep .paginator button:hover {
        background-color: #007bff;
        color: white;
    }*/

    /* ✅ 修改不同按鈕的內容，改成 FontAwesome 或 Unicode 圖標 */
    /*::deep .paginator button:first-child::before {
        content: "⏮";*/ /* 最前頁 (First) */
    /*}

    ::deep .paginator button:nth-child(2)::before {
        content: "◀";*/ /* 上一頁 (Previous) */
    /*}

    ::deep .paginator button:nth-last-child(2)::before {
        content: "▶";*/ /* 下一頁 (Next) */
    /*}

    ::deep .paginator button:last-child::before {
        content: "⏭";*/ /* 最後頁 (Last) */
    /*}*/

    /* ✅ 隱藏原本的按鈕文字 */
    [b-01ifdea9l5] .paginator button span {
        display: none;
    }

/*::deep .go-previous {
    background-color: #78c2ad;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M41.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l160 160c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L109.3 256 246.6 118.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-160 160z"/></svg>');
}*/