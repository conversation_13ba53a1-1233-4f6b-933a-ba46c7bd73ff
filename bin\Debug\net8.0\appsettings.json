{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "dbASH": "Server=yuntech-sql-managed-prod.ccb050153c99.database.windows.net,41432;;Database=ASH_DengueFever;User ID=*********;Password=*********;TrustServerCertificate=true;", // 測試
    "Oracle": "User ID=asx_web;Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521)))(CONNECT_DATA=(SID=tcxweb)(SERVER=DEDICATED)));Password=************;Pooling=true"
  },
  //"Debug": "Y",
  "AppID": "TCO_NONDIS",
  "SSO": {
    "Host": "https://webapp.yuntech.edu.tw/",
    "Verify": "http://webapp.yuntech.edu.tw/YuntechSSO/Account/VerifyAuthorizedToken",
    "Request": "http://webapp.yuntech.edu.tw/YuntechSSO/Account/GetRequestToken",
    "Login": "https://webapp.yuntech.edu.tw/YuntechSSO/Account/Login",
    "Logout": "https://webapp.yuntech.edu.tw/YuntechSSO/Account/Logout",
    "LoginEndPoint": "https://webapp.yuntech.edu.tw/RepuApply/Accounts/LoginEndPoint"
  },
  "SysSetting": {
    "SysUrl": "/",
    "DownLoadUrl": "api"
  },
  "CosmosDb": { // Azure Cosmos DB 設定
    "EndpointUri": "https://yuntech-japan-nosql.documents.azure.com:443/", // ★ 端點
    "PrimaryKey": "****************************************************************************************", // ★ 主要金鑰
    "DatabaseId": "Dashboard" // 資料庫名稱
  }
}
