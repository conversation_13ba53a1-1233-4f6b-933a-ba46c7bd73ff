﻿@page "/Internet"
@using System.Diagnostics
@using Dashboard_Yuntech.Service
@using Dashboard_Yuntech.Models.ChartModels
@using ApexCharts
@rendermode InteractiveServer
@inject WebScrapingService WebScrapingService
@inject ChartDataService ChartDataService
@inject IJSRuntime JSRuntime

<div>
    <div class="alert alert-info d-flex justify-content-between gap-4">
        <div class="fw-bold align-content-center"><i class="fa-solid fa-globe me-2"></i>網路流量資料圖表</div>
        <div>
            <button class="btn btn-sm btn-dark" @onclick="ScrapeAllTimeRanges" disabled="@isLoading">
                @if (isLoading)
                {
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    <span>更新中...</span>
                }
                else
                {
                    <span>手動更新</span>
                }
                @if (countdownSeconds > 0 && !isLoading)
                {
                    <span class="badge bg-dark rounded-pill ms-2">
                        <i class="fas fa-clock me-1"></i>
                        自動： @countdownSeconds s
                    </span>
                }
            </button>
        </div>
    </div>
    

    @if (isLoading)
    {
        <div class="text-center mt-4">
            <div class="spinner-border" role="status"></div>
            <p class="mt-2">正在爬取所有時間範圍的資料，請稍候...</p>
            @if (!string.IsNullOrEmpty(currentTimeRange))
            {
                <p class="text-muted">目前正在處理: @timeRangeDisplayNames[currentTimeRange]</p>
            }
        </div>
    }

    @if (allTimeRangeData.Any())
    {
            <!-- Hourly 每小時 -->
            @if (allTimeRangeData.ContainsKey("Hourly"))
            {
                var data = allTimeRangeData["Hourly"];
                @if (data.ChartData != null && data.ChartData.Any())
                {
                    <div class="mb-4">
                        <NetworkChart Year="@data.DataDate" Title="每20分鐘 (1分鐘間隔)"
                                      ChartData="@data.ChartData"
                                      ChartOptions="@data.ChartOptions"
                                      ModalId="networkTrafficModal_Hourly" />
                    </div>
                }
                else
                {
                    <div class="alert alert-info">
                        <strong>提示:</strong> 沒有可顯示的資料
                    </div>
                }
            }

            <!-- Daily 每日 -->
            @if (allTimeRangeData.ContainsKey("Daily"))
            {
                var data = allTimeRangeData["Daily"];
                @if (data.ChartData != null && data.ChartData.Any())
                {
                    <div class="mb-4">
                        <NetworkChart Year="@data.DataDate" Title="每2小時 (5分鐘間隔)"
                                      ChartData="@data.ChartData"
                                      ChartOptions="@data.ChartOptions"
                                      ModalId="networkTrafficModal_Daily" />
                    </div>
                }
                else
                {
                    <div class="alert alert-info">
                        <strong>提示:</strong> 沒有可顯示的資料
                    </div>
                }
            }

            <!-- Weekly 每週 -->
            @if (allTimeRangeData.ContainsKey("Weekly"))
            {
                var data = allTimeRangeData["Weekly"];
                @if (data.ChartData != null && data.ChartData.Any())
                {
                    <div class="mb-4">
                        <NetworkChart Year="@data.DataDate" Title="每日 (30分鐘間隔)"
                                      ChartData="@data.ChartData"
                                      ChartOptions="@data.ChartOptions"
                                      ModalId="networkTrafficModal_Weekly" />
                    </div>
                }
                else
                {
                    <div class="alert alert-info">
                        <strong>提示:</strong> 沒有可顯示的資料
                    </div>
                }
            }

            <!-- Monthly 每月 -->
            @if (allTimeRangeData.ContainsKey("Monthly"))
            {
                var data = allTimeRangeData["Monthly"];
                @if (data.ChartData != null && data.ChartData.Any())
                {
                    <div class="mb-4">
                        <NetworkChart Year="@data.DataDate" Title="每周 (2小時間隔)"
                                      ChartData="@data.ChartData"
                                      ChartOptions="@data.ChartOptions"
                                      ModalId="networkTrafficModal_Monthly" />
                    </div>
                }
                else
                {
                    <div class="alert alert-info">
                        <strong>提示:</strong> 沒有可顯示的資料
                    </div>
                }
            }

            <!-- Yearly 每年 -->
            @if (allTimeRangeData.ContainsKey("Yearly"))
            {
                var data = allTimeRangeData["Yearly"];
                @if (data.ChartData != null && data.ChartData.Any())
                {
                    <div class="mb-4">
                        <NetworkChart Year="@data.DataDate" Title="每月 (1天間隔)"
                                      ChartData="@data.ChartData"
                                      ChartOptions="@data.ChartOptions"
                                      ModalId="networkTrafficModal_Yearly" />
                    </div>
                }
                else
                {
                    <div class="alert alert-info">
                        <strong>提示:</strong> 沒有可顯示的資料
                    </div>
                }
            }
    }
</div>


@implements IDisposable

@code {
    private string cactiUrl = "http://cnms.yuntech.edu.tw/cacti/graph.php?action=view&local_graph_id=1711&rra_id=all";
    private bool isLoading = false;
    private string errorMessage = "";
    private string successMessage = "";
    private string currentTimeRange = "";

    // 自動重新爬取相關變數
    private int autoRefreshMinutes = 1; // 修改這個變數就能改變自動爬取間隔（分鐘）
    private int countdownSeconds = 0;
    private Timer? countdownTimer;
    private Timer? autoRefreshTimer;

    // 所有時間範圍的資料
    private List<string> timeRanges = new() { "Hourly", "Daily", "Weekly", "Monthly", "Yearly" };
    private Dictionary<string, string> timeRangeDisplayNames = new()
    {
        { "Hourly", "每小時 (1分鐘間隔)" },
        { "Daily", "每日 (5分鐘間隔)" },
        { "Weekly", "每週 (30分鐘間隔)" },
        { "Monthly", "每月 (2小時間隔)" },
        { "Yearly", "每年 (1天間隔)" }
    };

    // 儲存各時間範圍的資料
    private Dictionary<string, TimeRangeData> allTimeRangeData = new();
    
    public class TimeRangeData
    {
        public string CsvContent { get; set; } = "";
        public List<Dictionary<string, object>> CsvData { get; set; } = new();
        public List<NetworkTrafficModel> ChartData { get; set; } = new();
        public ApexChartOptions<NetworkTrafficModel> ChartOptions { get; set; } = new();
        public string DataDate { get; set; } = "";
        public string ErrorMessage { get; set; } = "";
    }
    
    protected override async Task OnInitializedAsync()
    {
        // 頁面載入時自動爬取所有時間範圍的資料
        await ScrapeAllTimeRanges();

        // 啟動自動重新爬取功能
        StartAutoRefresh();
    }

    private void StartAutoRefresh()
    {
        // 停止現有的計時器
        StopTimers();

        // 設定倒數計時器（每秒更新一次）
        countdownSeconds = autoRefreshMinutes * 60;
        countdownTimer = new Timer(async _ =>
        {
            countdownSeconds--;
            await InvokeAsync(StateHasChanged);

            if (countdownSeconds <= 0)
            {
                // 時間到了，執行自動爬取
                await InvokeAsync(async () =>
                {
                    if (!isLoading) // 確保沒有正在進行的爬取
                    {
                        await ScrapeAllTimeRanges();
                        StartAutoRefresh(); // 重新啟動倒數計時
                    }
                });
            }
        }, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }

    private void StopTimers()
    {
        countdownTimer?.Dispose();
        autoRefreshTimer?.Dispose();
        countdownTimer = null;
        autoRefreshTimer = null;
    }
    
    private void GenerateTestData()
    {
        try
        {
            allTimeRangeData.Clear();
            
            foreach (var timeRange in timeRanges)
            {
                var testData = new TimeRangeData
                {
                    ChartData = GenerateTestChartData(timeRange),
                    CsvData = new List<Dictionary<string, object>>(),
                    CsvContent = "",
                    DataDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    ErrorMessage = ""
                };
                
                // 設定獨立的圖表選項
                testData.ChartOptions = SetupChartOptions(timeRange);
                
                // 轉換圖表資料為表格資料
                testData.CsvData = testData.ChartData.Select(d => new Dictionary<string, object>
                {
                    ["時間"] = d.Time,
                    ["入站流量"] = d.Inbound,
                    ["出站流量"] = d.Outbound
                }).ToList();
                
                allTimeRangeData[timeRange] = testData;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"生成測試資料失敗: {ex.Message}";
        }
    }
    
    private List<NetworkTrafficModel> GenerateTestChartData(string timeRange)
    {
        var random = new Random(timeRange.GetHashCode()); // 使用timeRange作為種子，確保每次產生相同的資料
        var data = new List<NetworkTrafficModel>();
        var baseTime = DateTime.Now.AddHours(-24);
        
        // 根據時間範圍產生不同間隔的資料
        var (interval, count, baseInbound, baseOutbound) = timeRange switch
        {
            "Hourly" => (TimeSpan.FromMinutes(5), 12, 100000000, 80000000),    // 5分鐘間隔，12筆資料
            "Daily" => (TimeSpan.FromHours(1), 24, 150000000, 120000000),     // 1小時間隔，24筆資料
            "Weekly" => (TimeSpan.FromHours(6), 28, 200000000, 160000000),    // 6小時間隔，28筆資料
            "Monthly" => (TimeSpan.FromDays(1), 30, 250000000, 200000000),    // 1天間隔，30筆資料
            "Yearly" => (TimeSpan.FromDays(15), 24, 300000000, 240000000),    // 15天間隔，24筆資料
            _ => (TimeSpan.FromHours(1), 10, 100000000, 80000000)
        };
        
        for (int i = 0; i < count; i++)
        {
            var time = baseTime.Add(interval * i);
            
            // 產生有變化的流量數據
            var inboundVariation = random.Next(-30, 50) / 100.0; // -30% to +50%
            var outboundVariation = random.Next(-20, 40) / 100.0; // -20% to +40%
            
            var inbound = (int)(baseInbound * (1 + inboundVariation));
            var outbound = (int)(baseOutbound * (1 + outboundVariation));
            
            data.Add(new NetworkTrafficModel
            {
                Time = time.ToString("yyyy MM dd HH mm"),
                Inbound = Math.Max(0, inbound),
                Outbound = Math.Max(0, outbound),
                OriginalTimeFormat = time.ToString("yyyy/M/d HH:mm")
            });
        }
        
        return data;
    }
    
    private async Task ScrapeAllTimeRanges()
    {
        try
        {
            isLoading = true;
            errorMessage = "";
            successMessage = "";
            allTimeRangeData.Clear();
            StateHasChanged();

            if (string.IsNullOrWhiteSpace(cactiUrl))
            {
                errorMessage = "請輸入 Cacti 圖表 URL";
                return;
            }

            int successCount = 0;
            int totalCount = timeRanges.Count;

            foreach (var timeRange in timeRanges)
            {
                try
                {
                    currentTimeRange = timeRange;
                    StateHasChanged();

                    // 爬取這個時間範圍的資料
                    var csvContent = await WebScrapingService.ScrapeAndDownloadCsvAsync(cactiUrl, timeRange);
                    
                    var timeRangeData = new TimeRangeData();
                    
                    if (string.IsNullOrWhiteSpace(csvContent))
                    {
                        timeRangeData.ErrorMessage = "下載的 CSV 內容為空";
                    }
                    else
                    {
                        // 解析 CSV 資料
                        timeRangeData.CsvContent = csvContent;
                        timeRangeData.CsvData = WebScrapingService.ParseCsvContent(csvContent);
                        
                        if (timeRangeData.CsvData.Any())
                        {
                            // 取得資料日期
                            timeRangeData.DataDate = WebScrapingService.GetFirstDataDate(csvContent);
                            
                            // 轉換為圖表資料，並根據時間範圍進行聚合
                            timeRangeData.ChartData = WebScrapingService.ConvertToChartData(csvContent, timeRange);
                            
                            // 設定圖表選項
                            timeRangeData.ChartOptions = SetupChartOptions(timeRange);
                            
                            successCount++;
                        }
                        else
                        {
                            timeRangeData.ErrorMessage = "CSV 資料解析失敗或無資料";
                        }
                    }
                    
                    allTimeRangeData[timeRange] = timeRangeData;
                }
                catch (Exception ex)
                {
                    allTimeRangeData[timeRange] = new TimeRangeData
                    {
                        ErrorMessage = $"處理失敗: {ex.Message}"
                    };
                }
            }

            currentTimeRange = "";
            successMessage = $"完成資料爬取！成功處理 {successCount}/{totalCount} 個時間範圍";
            
            if (successCount == 0)
            {
                errorMessage = "所有時間範圍的資料都無法成功載入";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"批次爬取失敗: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            currentTimeRange = "";
            StateHasChanged();

            // 重新啟動自動重新爬取倒數計時
            StartAutoRefresh();
        }
    }



    private ApexChartOptions<NetworkTrafficModel> SetupChartOptions(string timeRange)
    {
        // 為每個時間範圍創建完全獨立的圖表選項物件
        // 使用不同的顏色種子來確保物件的獨立性
        var colorSeed = timeRange switch
        {
            "Hourly" => 0,
            "Daily" => 1,
            "Weekly" => 2,
            "Monthly" => 3,
            "Yearly" => 4,
            _ => 0
        };
        
        var colors = ChartDataService.GetStandardColors(2, 0);
        
        // 為每個時間範圍創建獨立的圖表選項
        var chartOptions = ChartDataService.CreateAreaChartOptions<NetworkTrafficModel>(
            colors: colors,
            showDataLabels: false,
            isSpline: true,
            isStacked: false,
            isMarkers: true,
            height:500
        );

        // 針對網路流量圖表的特殊設定 - 每次都創建新的物件
        chartOptions.Xaxis = new XAxis();

        // 根據時間範圍設定不同的標籤顯示邏輯
        chartOptions.Xaxis.Labels = new XAxisLabels
        {
            Style = new AxisLabelStyle { FontSize = "12px", Colors = new List<string> { "#fff" } },
            ShowDuplicates = false,
            Rotate = 0,
            RotateAlways = false
        };

        switch (timeRange)
        {
            case "Hourly":
                // 每20分鐘顯示標籤，只顯示 HH:mm 格式
                chartOptions.Xaxis.Labels.Formatter = @"function(value, timestamp, index, opts) {
                    if (!value) return '';
                    var parts = value.split(' ');
                    if (parts.length !== 5) return '';

                    var hour = parseInt(parts[3]);
                    var minute = parseInt(parts[4]);
                    if (minute % 20 === 0) {
                        return hour + ':' + (minute < 10 ? '0' + minute : minute);
                    }
                    return '';
                }";
                break;

            case "Daily":
                // 每2小時顯示標籤
                chartOptions.Xaxis.Labels.Formatter = @"function(value, timestamp, index, opts) {
                    if (!value) return '';
                    var parts = value.split(' ');
                    if (parts.length !== 5) return '';

                    var hour = parseInt(parts[3]);
                    var minute = parseInt(parts[4]);
                    if (hour % 2 === 0 && minute === 0) {
                        return hour + ':' + (minute < 10 ? '0' + minute : minute);
                    }

                    return '';
                }";
                break;
            case "Weekly":
                // 每日顯示標籤
                chartOptions.Xaxis.Labels.Formatter = @"function(value, timestamp, index, opts) {
                    if (!value) return '';
                    var parts = value.split(' ');
                    if (parts.length !== 5) return '';

                    var mon = parseInt(parts[1]);
                    var day = parseInt(parts[2]);
                    var hour = parseInt(parts[3]);
                    var minute = parseInt(parts[4]);

                    if (hour % 12 === 0 && minute === 0) {
                        return mon + '/' + day;
                    }
                    return '';
                }";
                break;

            case "Monthly":
                // 每周顯示標籤，顯示該周為整年的第幾周
                chartOptions.Xaxis.Labels.Formatter = @"function(value, timestamp, index, opts) {
                    if (!value) return '';
                    var parts = value.split(' ');
                    if (parts.length !== 5) return '';
                    
                    // 解析日期：year, month, day
                    var year = parseInt(parts[0]);
                    var month = parseInt(parts[1]);
                    var day = parseInt(parts[2]);
                    var hour = parseInt(parts[3]);
                    var minute = parseInt(parts[4]);

                    // 創建 Date 物件
                    var date = new Date(year, month - 1, day);
                    
                    // 計算該日期是一年中的第幾周
                    var startOfYear = new Date(year, 0, 1);
                    var diff = date - startOfYear;
                    var weekNumber = Math.ceil((diff / (24 * 60 * 60 * 1000) + startOfYear.getDay() + 1) / 7);

                    if (day % 7 == 0 && hour > 20) {
                        return 'week' + weekNumber;
                    }
                    return '';
                }";
                break;

            case "Yearly":
                // 每月顯示標籤
                chartOptions.Xaxis.Labels.Formatter = @"function(value, timestamp, index, opts) {
                    if (!value) return '';
                    var parts = value.split(' ');
                    if (parts.length !== 5) return '';
                    
                    // 解析月份
                    var year = parseInt(parts[0]);
                    var month = parseInt(parts[1]);
                    var day = parseInt(parts[2]);
                    var hour = parseInt(parts[3]);
                    var minute = parseInt(parts[4]);
                    
                    var monthNames = ['1月', '2月', '3月', '4月', '5月', '6月',
                                    '7月', '8月', '9月', '10月', '11月', '12月'];
                    var label = monthNames[month - 1] || monthNames[0];

                    // 閏年                    
                    if(year % 4 ==0 && year % 100 != 0){
                        if(month == 2 && day % 29 ==0)
                        {
                            return label;
                        }
                        else if (day % 30 == 0){
                            return label;
                        }
                    }
                    else{
                    if(month == 2 && day % 28 ==0)
                        {
                            return label;
                        }
                        else if (day % 30 == 0){
                            return label;
                        }
                    }

                    return '';
                }";
                break;

            default:
                // 預設情況，不添加特殊格式化
                break;
        }
        
        // 確保 Yaxis 是新的陣列
        chartOptions.Yaxis = new List<YAxis> { new YAxis() };
        chartOptions.Yaxis[0].Title = new AxisTitle {
            Text = "流量 (bps)",
            Style = new AxisTitleStyle { FontSize = "14px", Color = "#fff" }
        };
        
        // 設定圖例 - 創建新的 Legend 物件
        chartOptions.Legend = new Legend
        {
            Position = LegendPosition.Top,
            FontSize = "14px"
        };
        
        return chartOptions;
    }

    public void Dispose()
    {
        StopTimers();
    }
}